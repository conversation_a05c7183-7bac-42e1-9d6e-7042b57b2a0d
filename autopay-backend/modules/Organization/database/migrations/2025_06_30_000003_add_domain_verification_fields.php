<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('domains', static function (Blueprint $table) {


            // SSL configuration
            $table->boolean('ssl_enabled')->default(false)->after('is_default');
            $table->timestamp('ssl_expires_at')->nullable()->after('ssl_enabled');

            // Domain type and status
            $table->string('domain_type')->default('subdomain')->after('hostname'); // 'subdomain', 'custom'
            $table->string('status')->default('pending')->after('domain_type'); // 'pending', 'active', 'failed', 'suspended'

            // Primary domain flag (one per organization)
            $table->boolean('is_primary')->default(false)->after('is_default');

            // DNS configuration
            $table->json('dns_records')->nullable()->after('custom_config'); // Required DNS records

            // Add indexes
            $table->index(['domain_type']);
            $table->index(['status']);
            $table->index(['is_primary']);
            $table->index(['organization_id', 'is_primary']);
            $table->index(['organization_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('domains', function (Blueprint $table) {
            $table->dropIndex(['domain_type']);
            $table->dropIndex(['status']);
            $table->dropIndex(['is_primary']);
            $table->dropIndex(['organization_id', 'is_primary']);
            $table->dropIndex(['organization_id', 'status']);

            $table->dropColumn([
                'ssl_enabled',
                'ssl_expires_at',
                'domain_type',
                'status',
                'is_primary',
                'dns_records'
            ]);
        });
    }
};
