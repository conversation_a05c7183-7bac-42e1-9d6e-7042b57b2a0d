<?php

namespace Modules\Core\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Core\Models\Domain;
use Modules\Core\Http\Requests\StoreDomainRequest;
use Modules\Core\Http\Requests\UpdateDomainRequest;
use Modules\Core\Http\Requests\SetupDomainRequest;
use Modules\Organization\Traits\OrganizationFromRequest;

use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class DomainController extends Controller
{
    use OrganizationFromRequest;



    /**
     * Get domain configuration by hostname.
     */
    public function getConfig(Request $request): Response
    {
        $hostname = $request->get('hostname', $request->getHost());

        $cacheKey = "domain_config_{$hostname}";

        $config = Cache::remember($cacheKey, 3600, function () use ($hostname) {
            $domain = Domain::byHostname($hostname)->active()->first();

            if (!$domain) {
                // Fallback to default domain
                $domain = Domain::default()->active()->first();
            }

            return $domain?->config;
        });

        if (!$config) {
            return ResponseHelper::error('Domain configuration not found', null, 404);
        }

        return ResponseHelper::success(data: $config);
    }

    /**
     * Get all domains for the organization.
     */
    public function index(Request $request): Response
    {
        $organization = $this->getOrganizationFromRequest($request);

        if (!$organization) {
            return ResponseHelper::error('Organization not found', null, 404);
        }

        $domains = Domain::byOrganization($organization->id)
                         ->active()
                         ->orderBy('is_primary', 'desc')
                         ->orderBy('created_at', 'desc')
                         ->get();

        return ResponseHelper::success(data: $domains);
    }

    /**
     * Create a new domain.
     */
    public function store(StoreDomainRequest $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (!$organization) {
                return ResponseHelper::error('Organization not found', null, 404);
            }

            $data = $request->validated();

            // Automatically set organization_id from route
            $data['organization_id'] = $organization->id;

            // Check hostname availability
            if (!$this->isHostnameAvailable($data['hostname'])) {
                throw ValidationException::withMessages([
                    'hostname' => ['The hostname is already taken.']
                ]);
            }

            $domain = Domain::create($data);

            // Clear cache
            $this->clearDomainCache($data['hostname']);

            return ResponseHelper::success('Domain created successfully', $domain, 201);
        } catch (ValidationException $e) {
            return ResponseHelper::error('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Get a specific domain.
     */
    public function show(string $id, Request $request): Response
    {
        $organization = $this->getOrganizationFromRequest($request);

        if (!$organization) {
            return ResponseHelper::error('Organization not found', null, 404);
        }

        $domain = Domain::where('id', $id)
                        ->where('organization_id', $organization->id)
                        ->first();

        if (!$domain) {
            return ResponseHelper::error('Domain not found', null, 404);
        }

        return ResponseHelper::success(data: $domain);
    }

    /**
     * Update a domain.
     */
    public function update(UpdateDomainRequest $request, string $id): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (!$organization) {
                return ResponseHelper::error('Organization not found', null, 404);
            }

            $domain = Domain::where('id', $id)
                            ->where('organization_id', $organization->id)
                            ->first();

            if (!$domain) {
                return ResponseHelper::error('Domain not found', null, 404);
            }

            $data = $request->validated();

            // Check hostname availability if hostname is being changed
            if (isset($data['hostname']) && $data['hostname'] !== $domain->hostname) {
                if (!$this->isHostnameAvailable($data['hostname'], $id)) {
                    throw ValidationException::withMessages([
                        'hostname' => ['The hostname is already taken.']
                    ]);
                }
            }

            $domain->update($data);

            // Clear cache for both old and new hostnames
            $this->clearDomainCache($domain->hostname);
            if (isset($data['hostname'])) {
                $this->clearDomainCache($data['hostname']);
            }

            return ResponseHelper::success('Domain updated successfully');
        } catch (ValidationException $e) {
            return ResponseHelper::error('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Delete a domain.
     */
    public function destroy(string $id, Request $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (!$organization) {
                return ResponseHelper::error('Organization not found', null, 404);
            }

            $domain = Domain::where('id', $id)
                            ->where('organization_id', $organization->id)
                            ->first();

            if (!$domain) {
                return ResponseHelper::error('Domain not found', null, 404);
            }

            if ($domain->is_default) {
                return ResponseHelper::error('Cannot delete the default domain', null, 400);
            }

            $domain->delete();

            // Clear cache
            $this->clearDomainCache($domain->hostname);

            return ResponseHelper::success('Domain deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Set domain as default.
     */
    public function setDefault(string $id, Request $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (!$organization) {
                return ResponseHelper::error('Organization not found', null, 404);
            }

            $domain = Domain::where('id', $id)
                            ->where('organization_id', $organization->id)
                            ->first();

            if (!$domain) {
                return ResponseHelper::error('Domain not found', null, 404);
            }

            // First, unset all other domains as default within this organization
            Domain::where('organization_id', $organization->id)
                  ->where('is_default', true)
                  ->update(['is_default' => false]);

            // Then set the specified domain as default
            $domain->update(['is_default' => true]);

            // Clear all domain caches since default status changed
            $domains = Domain::byOrganization($organization->id)->active()->get();
            foreach ($domains as $domainItem) {
                $this->clearDomainCache($domainItem->hostname);
            }

            return ResponseHelper::success('Domain set as default successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Setup domain for organization.
     */
    public function setupDomain(SetupDomainRequest $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (!$organization) {
                return ResponseHelper::error('Organization not found', null, 404);
            }

            // Validate domain format
            $errors = $this->validateDomainFormat($request->hostname);
            if (!empty($errors)) {
                return ResponseHelper::error('Domain validation failed', $errors, 422);
            }

            $domain = $this->setupDomainForOrganization(
                $request->hostname,
                $organization->id,
                $request->only(['name', 'description'])
            );

            return ResponseHelper::success('Domain setup initiated', $domain, 201);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }



    /**
     * Set domain as primary.
     */
    public function setPrimary(string $id, Request $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (!$organization) {
                return ResponseHelper::error('Organization not found', null, 404);
            }

            $domain = Domain::where('id', $id)
                            ->where('organization_id', $organization->id)
                            ->first();

            if (!$domain) {
                return ResponseHelper::error('Domain not found', null, 404);
            }

            $domain->setAsPrimary();
            $this->clearDomainCache($domain->hostname);

            return ResponseHelper::success('Domain set as primary successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }


    /**
     * Check if hostname is available.
     */
    private function isHostnameAvailable(string $hostname, ?string $excludeId = null): bool
    {
        $query = Domain::where('hostname', $hostname);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    /**
     * Clear domain cache.
     */
    private function clearDomainCache(string $hostname): void
    {
        Cache::forget("domain_config_{$hostname}");
    }


}
