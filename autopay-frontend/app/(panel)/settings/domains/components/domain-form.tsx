'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { useAuth } from '@/lib/hooks/useAuth'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { AlertCircle, Info } from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

const domainSchema = z.object({
  hostname: z
    .string()
    .min(1, 'Hostname là bắt buộc')
    .regex(
      /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
      'Định dạng hostname không hợp lệ'
    ),
  name: z.string().min(1, 'Tên domain là bắt buộc').max(255, 'Tên domain quá dài'),
  description: z.string().optional(),
})

type DomainFormData = z.infer<typeof domainSchema>

interface Domain {
  id: string
  hostname: string
  name: string
  description?: string
  domain_type: 'subdomain' | 'custom'
  status: string
  is_primary: boolean
}

interface DomainFormProps {
  domain?: Domain | null
  onClose: () => void
}

export function DomainForm({ domain, onClose }: DomainFormProps) {
  const { user } = useAuth()
  const [error, setError] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  // Create domain mutation
  const { isPending: isCreating, mutate: createDomain } = useMutation({
    mutationFn: (data: DomainFormData & { organization_id: string }) =>
      queryFetchHelper('/domains/setup', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onSuccess: (response) => {
      if (response.success) {
        toast.success(response.message || 'Domain đã được tạo thành công')
        onClose()
      } else {
        if (response.errors) {
          setValidationErrors(Array.isArray(response.errors) ? response.errors : [response.message])
        } else {
          setError(response.message || 'Có lỗi xảy ra khi tạo domain')
        }
      }
    },
    onError: (error: any) => {
      if (error.errors) {
        setValidationErrors(Array.isArray(error.errors) ? error.errors : [error.message])
      } else {
        setError(error.message || 'Có lỗi xảy ra khi tạo domain')
      }
    },
  })

  // Update domain mutation
  const { isPending: isUpdating, mutate: updateDomain } = useMutation({
    mutationFn: (data: DomainFormData) =>
      queryFetchHelper(`/domains/${domain?.id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    onSuccess: (response) => {
      if (response.success) {
        toast.success(response.message || 'Domain đã được cập nhật thành công')
        onClose()
      } else {
        if (response.errors) {
          setValidationErrors(Array.isArray(response.errors) ? response.errors : [response.message])
        } else {
          setError(response.message || 'Có lỗi xảy ra khi cập nhật domain')
        }
      }
    },
    onError: (error: any) => {
      if (error.errors) {
        setValidationErrors(Array.isArray(error.errors) ? error.errors : [error.message])
      } else {
        setError(error.message || 'Có lỗi xảy ra khi cập nhật domain')
      }
    },
  })

  const form = useForm<DomainFormData>({
    resolver: zodResolver(domainSchema),
    defaultValues: {
      hostname: domain?.hostname || '',
      name: domain?.name || '',
      description: domain?.description || '',
    },
  })

  const isEditing = !!domain
  const isLoading = isCreating || isUpdating

  const onSubmit = (data: DomainFormData) => {
    setError(null)
    setValidationErrors([])

    // Get current organization ID from user
    const orgId = user?.current_organization?.id

    if (!orgId) {
      setError('Không tìm thấy thông tin tổ chức. Vui lòng đăng nhập lại.')
      return
    }

    if (isEditing) {
      updateDomain(data)
    } else {
      createDomain({
        ...data,
        organization_id: orgId,
      })
    }
  }

  const getDomainTypeInfo = (hostname: string) => {
    const appDomain = 'autopay.vn' // Replace with actual app domain
    const isSubdomain = hostname.endsWith(`.${appDomain}`)

    return {
      type: isSubdomain ? 'subdomain' : 'custom',
      description: isSubdomain
        ? 'Subdomain của AutoPay - không cần xác thực DNS'
        : 'Domain tùy chỉnh - cần xác thực quyền sở hữu',
    }
  }

  const watchedHostname = form.watch('hostname')
  const domainInfo = watchedHostname ? getDomainTypeInfo(watchedHostname) : null

  return (
    <Dialog
      open={true}
      onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Chỉnh sửa Domain' : 'Thêm Domain mới'}</DialogTitle>
          <DialogDescription>
            {isEditing ? 'Cập nhật thông tin domain của bạn' : 'Thêm domain mới cho tổ chức của bạn'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {validationErrors.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <ul className="list-inside list-disc space-y-1">
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            <FormField
              control={form.control}
              name="hostname"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Hostname</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="example.com hoặc myorg.autopay.vn"
                      {...field}
                      disabled={isEditing} // Cannot change hostname when editing
                    />
                  </FormControl>
                  <FormDescription>Nhập domain hoặc subdomain bạn muốn sử dụng</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {domainInfo && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>Loại domain:</strong> {domainInfo.type === 'custom' ? 'Custom Domain' : 'Subdomain'}
                  <br />
                  {domainInfo.description}
                </AlertDescription>
              </Alert>
            )}

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên hiển thị</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Tên domain để hiển thị"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>Tên này sẽ được hiển thị trong danh sách domain</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mô tả (tùy chọn)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Mô tả về domain này..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>Mô tả ngắn về mục đích sử dụng domain</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}>
                Hủy
              </Button>
              <Button
                type="submit"
                disabled={isLoading}>
                {isLoading ? 'Đang xử lý...' : isEditing ? 'Cập nhật' : 'Thêm Domain'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
