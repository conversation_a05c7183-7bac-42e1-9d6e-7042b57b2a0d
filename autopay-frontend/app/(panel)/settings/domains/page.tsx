'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/lib/hooks/useAuth'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AlertCircle, CheckCircle, Globe, Plus, Settings } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'
import { DomainForm } from './components/domain-form'
import { DomainVerification } from './components/domain-verification'

interface Domain {
  id: string
  hostname: string
  name: string
  description?: string
  domain_type: 'subdomain' | 'custom'
  status: 'pending' | 'active' | 'failed' | 'suspended'
  is_verified: boolean
  is_primary: boolean
  is_active: boolean
  verified_at?: string
  created_at: string
}

export default function DomainsPage() {
  const { user } = useAuth()
  const [showForm, setShowForm] = useState(false)
  const [showVerification, setShowVerification] = useState(false)
  const [selectedDomain, setSelectedDomain] = useState<Domain | null>(null)
  const queryClient = useQueryClient()

  // Use React Query to fetch domains
  const {
    data: domains = [],
    isLoading,
    error,
  } = useQuery<Domain[]>({
    queryKey: ['domains', user?.current_organization?.id],
    queryFn: async (): Promise<Domain[]> => {
      // Get current organization ID from user
      const orgId = user?.current_organization?.id

      if (!orgId) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }

      const response = await queryFetchHelper(`/organizations/${orgId}/domains`)
      return response.data
    },
    enabled: !!user?.current_organization?.id, // Only fetch when orgId is available
  })

  // Mutation for setting primary domain
  const setPrimaryMutation = useMutation({
    mutationFn: async (domainId: string): Promise<void> => {
      await queryFetchHelper(`/domains/${domainId}/set-primary`, {
        method: 'POST',
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['domains'] })
      toast.success('Domain chính đã được cập nhật')
    },
    onError: (error: any) => {
      console.error('Error setting primary domain:', error)
      toast.error('Có lỗi xảy ra khi đặt domain chính')
    },
  })

  const handleAddDomain = () => {
    setSelectedDomain(null)
    setShowForm(true)
  }

  const handleEditDomain = (domain: Domain) => {
    setSelectedDomain(domain)
    setShowForm(true)
  }

  const handleVerifyDomain = (domain: Domain) => {
    setSelectedDomain(domain)
    setShowVerification(true)
  }

  const handleSetPrimary = (domainId: string): void => {
    setPrimaryMutation.mutate(domainId)
  }

  const getStatusBadge = (domain: Domain) => {
    if (!domain.is_verified) {
      return (
        <Badge
          variant="outline"
          className="text-yellow-600">
          Chưa xác thực
        </Badge>
      )
    }

    switch (domain.status) {
      case 'active':
        return (
          <Badge
            variant="default"
            className="bg-green-500">
            Hoạt động
          </Badge>
        )
      case 'pending':
        return (
          <Badge
            variant="outline"
            className="text-blue-600">
            Đang chờ
          </Badge>
        )
      case 'failed':
        return <Badge variant="destructive">Lỗi</Badge>
      case 'suspended':
        return (
          <Badge
            variant="outline"
            className="text-red-600">
            Tạm dừng
          </Badge>
        )
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
          <h3 className="mb-2 text-lg font-semibold">Có lỗi xảy ra</h3>
          <p className="text-muted-foreground">
            {error instanceof Error ? error.message : 'Không thể tải danh sách domain'}
          </p>
        </div>
      </div>
    )
  }

  if (!user?.current_organization?.id) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 h-12 w-12 text-yellow-500" />
          <h3 className="mb-2 text-lg font-semibold">Không tìm thấy tổ chức</h3>
          <p className="text-muted-foreground">Vui lòng đăng nhập lại hoặc chọn tổ chức</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Quản lý Domain</h1>
          <p className="text-muted-foreground">Quản lý các domain cho tổ chức của bạn</p>
        </div>
        <Button onClick={handleAddDomain}>
          <Plus className="mr-2 h-4 w-4" />
          Thêm Domain
        </Button>
      </div>

      <div className="grid gap-4">
        {domains.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Globe className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">Chưa có domain nào</h3>
              <p className="text-muted-foreground mb-4 text-center">
                Thêm domain đầu tiên để bắt đầu sử dụng white-label
              </p>
              <Button onClick={handleAddDomain}>
                <Plus className="mr-2 h-4 w-4" />
                Thêm Domain
              </Button>
            </CardContent>
          </Card>
        ) : (
          domains.map((domain: Domain) => (
            <Card key={domain.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Globe className="h-5 w-5" />
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {domain.hostname}
                        {domain.is_primary && (
                          <Badge
                            variant="outline"
                            className="text-blue-600">
                            Chính
                          </Badge>
                        )}
                      </CardTitle>
                      <CardDescription>{domain.name}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(domain)}
                    <Badge variant="outline">{domain.domain_type === 'custom' ? 'Custom' : 'Subdomain'}</Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-muted-foreground flex items-center gap-4 text-sm">
                    {domain.is_verified ? (
                      <div className="flex items-center gap-1">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        Đã xác thực
                      </div>
                    ) : (
                      <div className="flex items-center gap-1">
                        <AlertCircle className="h-4 w-4 text-yellow-500" />
                        Chưa xác thực
                      </div>
                    )}
                    <span>Tạo: {new Date(domain.created_at).toLocaleDateString('vi-VN')}</span>
                  </div>

                  <div className="flex items-center gap-2">
                    {!domain.is_verified && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleVerifyDomain(domain)}>
                        Xác thực
                      </Button>
                    )}

                    {domain.is_verified && !domain.is_primary && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetPrimary(domain.id)}>
                        Đặt làm chính
                      </Button>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditDomain(domain)}>
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {showForm && (
        <DomainForm
          domain={selectedDomain}
          onClose={() => {
            setShowForm(false)
            setSelectedDomain(null)
            queryClient.invalidateQueries({ queryKey: ['domains'] })
          }}
        />
      )}

      {showVerification && selectedDomain && (
        <DomainVerification
          domain={selectedDomain}
          onClose={() => {
            setShowVerification(false)
            setSelectedDomain(null)
            queryClient.invalidateQueries({ queryKey: ['domains'] })
          }}
        />
      )}
    </div>
  )
}
