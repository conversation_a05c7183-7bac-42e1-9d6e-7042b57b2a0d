{"printWidth": 120, "semi": false, "bracketSameLine": true, "trailingComma": "es5", "tabWidth": 2, "singleQuote": true, "bracketSpacing": true, "arrowParens": "always", "singleAttributePerLine": true, "htmlWhitespaceSensitivity": "ignore", "vueIndentScriptAndStyle": false, "plugins": ["prettier-plugin-organize-imports", "prettier-plugin-tailwindcss", "@shufo/prettier-plugin-blade", "@prettier/plugin-php"], "tailwindFunctions": ["cn"], "tailwindPreserveWhitespace": false, "organizeImportsSkipDestructiveCodeActions": true}